{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../custom-component/index.ts"], "names": [], "mappings": ";;AAUA,0CAeC;AAzBD,2DAAuI;AACvI,+CAA+C;AAS/C,SAAgB,eAAe,CAAC,OAAe;IAC7C,OAAO,CAAC,IAAU,EAAE,OAAyB,EAAE,EAAE;QAC/C,MAAM,cAAc,GAAG,IAAA,kBAAK,EAAC,IAAA,gBAAG,EAAC,SAAS,CAAC,EAAE;YAC3C,IAAA,qBAAQ,EAAC;gBACP,GAAG,cAAO;gBACV,GAAG,OAAO;gBACV,MAAM,EAAE,OAAO,CAAC,IAAI;aACrB,CAAC;YACF,IAAA,iBAAI,EAAC,OAAO,CAAC,IAAI,IAAI,SAAS,CAAC;SAChC,CAAC,CAAC;QAEH,OAAO,IAAA,kBAAK,EAAC;YACX,IAAA,sBAAS,EAAC,cAAc,EAAE,0BAAa,CAAC,OAAO,CAAC;SACjD,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { Rule, Tree, SchematicContext, apply, url, template, move, mergeWith, chain, MergeStrategy } from '@angular-devkit/schematics';\r\nimport { strings } from '@angular-devkit/core';\r\n\r\ninterface Schema {\r\n  name: string;\r\n  path?: string;\r\n  style?: string;\r\n  skipTests?: boolean;\r\n}\r\n\r\nexport function customComponent(options: Schema): Rule {\r\n  return (tree: Tree, context: SchematicContext) => {\r\n    const templateSource = apply(url('./files'), [\r\n      template({\r\n        ...strings,\r\n        ...options,\r\n        'name': options.name\r\n      }),\r\n      move(options.path || 'src/app')\r\n    ]);\r\n\r\n    return chain([\r\n      mergeWith(templateSource, MergeStrategy.Default)\r\n    ]);\r\n  };\r\n}"]}
{"name": "emoji-regex", "version": "9.2.2", "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "homepage": "https://mths.be/emoji-regex", "main": "index.js", "types": "index.d.ts", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/emoji-regex.git"}, "bugs": "https://github.com/mathiasbynens/emoji-regex/issues", "files": ["LICENSE-MIT.txt", "index.js", "index.d.ts", "RGI_Emoji.js", "RGI_Emoji.d.ts", "text.js", "text.d.ts", "es2015"], "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src es2015_types -D -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/preset-env": "^7.4.4", "@unicode/unicode-13.0.0": "^1.0.3", "mocha": "^6.1.4", "regexgen": "^1.3.0"}}
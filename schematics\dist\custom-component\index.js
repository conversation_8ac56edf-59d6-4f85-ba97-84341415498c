"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.customComponent = customComponent;
const schematics_1 = require("@angular-devkit/schematics");
const core_1 = require("@angular-devkit/core");
function customComponent(options) {
    return (tree, context) => {
        const templateSource = (0, schematics_1.apply)((0, schematics_1.url)('./files'), [
            (0, schematics_1.template)({
                ...core_1.strings,
                ...options,
                'name': options.name
            }),
            (0, schematics_1.move)((options.path || 'src/app') + '/' + core_1.strings.dasherize(options.name))
        ]);
        return (0, schematics_1.chain)([
            (0, schematics_1.mergeWith)(templateSource, schematics_1.MergeStrategy.Default)
        ]);
    };
}
//# sourceMappingURL=index.js.map
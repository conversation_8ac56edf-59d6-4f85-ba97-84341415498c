import { Rule, Tree, SchematicContext, apply, url, template, move, mergeWith, chain, MergeStrategy } from '@angular-devkit/schematics';
import { strings } from '@angular-devkit/core';

interface Schema {
  name: string;
  path?: string;
  style?: string;
  skipTests?: boolean;
}

export function customComponent(options: Schema): Rule {
  return (tree: Tree, context: SchematicContext) => {
    const templateSource = apply(url('./files'), [
      template({
        ...strings,
        ...options,
        'name': options.name
      }),
      move((options.path || 'src/app') + '/' + strings.dasherize(options.name))
    ]);

    return chain([
      mergeWith(templateSource, MergeStrategy.Default)
    ]);
  };
}
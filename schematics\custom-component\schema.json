{"$schema": "http://json-schema.org/schema", "$id": "SchematicCustomComponent", "title": "Custom Component Options Schema", "type": "object", "properties": {"name": {"type": "string", "description": "The name of the component.", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use for the component?"}, "path": {"type": "string", "format": "path", "description": "The path to create the component.", "default": "src/app"}, "style": {"type": "string", "description": "The file extension or preprocessor to use for style files.", "default": "css"}, "skipTests": {"type": "boolean", "description": "When true, does not create \"spec.ts\" test files.", "default": false}}, "required": ["name"]}